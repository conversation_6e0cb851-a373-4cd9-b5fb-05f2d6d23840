{"name": "project-controls", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "reset-db": "supabase db reset && supabase gen types typescript --local > src/lib/database.types.ts && pnpm format", "test:unit": "vitest run", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test", "test:mcp": "playwright test --config=playwright-mcp.config.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test:server": "vitest --project=server", "test:client": "vitest --project=client", "test:ssr": "vitest --project=ssr"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0", "@eslint/compat": "^1.2.9", "@eslint/js": "^9.28.0", "@lucide/svelte": "^0.513.0", "@playwright/test": "^1.52.0", "@storybook/addon-docs": "9.1.0-alpha.2", "@storybook/addon-svelte-csf": "5.0.0-next.30", "@storybook/addon-vitest": "9.1.0-alpha.2", "@storybook/sveltekit": "9.1.0-alpha.2", "@sveltejs/adapter-vercel": "^5.7.2", "@sveltejs/kit": "^2.21.2", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.8", "@tanstack/table-core": "^8.21.3", "@vitest/browser": "3.2.1", "@vitest/coverage-v8": "3.2.1", "bits-ui": "^2.4.1", "clsx": "^2.1.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-storybook": "9.1.0-alpha.2", "eslint-plugin-svelte": "^3.9.1", "formsnap": "^2.0.1", "globals": "^16.2.0", "layerchart": "2.0.0-next.6", "mode-watcher": "^1.0.7", "paneforge": "1.0.0-next.5", "phosphor-svelte": "^3.0.1", "playwright": "^1.52.0", "prettier": "^3.5.3", "prettier-plugin-sql": "^0.19.1", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "pretty-quick": "^4.2.2", "simple-git-hooks": "^2.13.0", "storybook": "9.1.0-alpha.2", "supabase": "^2.24.3", "svelte": "^5.33.14", "svelte-check": "^4.2.1", "svelte-sonner": "^1.0.4", "sveltekit-flash-message": "^2.4.6", "sveltekit-superforms": "^2.26.1", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "vite": "^6.3.5", "vitest": "^3.2.1", "vitest-browser-svelte": "^0.1.0", "zod": "^3.25.51"}, "dependencies": {"@internationalized/date": "^3.8.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.10", "date-fns": "^4.1.0", "resend": "^4.5.2", "uid": "^2.0.2"}, "packageManager": "pnpm@10.11.0", "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild", "msw", "simple-git-hooks", "supabase", "svelte-preprocess"], "ignoredBuiltDependencies": ["@tailwindcss/oxide"]}}