import { z } from 'zod';
import { MEGABYTE } from '$lib/components/ui/file-drop-zone';

const fileSchema = z
	.instanceof(File)
	.refine((file) => file.size <= 10 * MEGABYTE, {
		message: 'File must be 10MB or smaller',
	})
	.refine((file) => file.name.toLowerCase().endsWith('.xlsx'), {
		message: 'Only .xlsx files are allowed',
	});

export const schema = z.object({
	attachments: z.array(fileSchema).min(1),
});

export type Schema = z.input<typeof schema>;
