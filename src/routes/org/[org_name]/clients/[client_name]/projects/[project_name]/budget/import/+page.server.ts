import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { requireUser } from '$lib/server/auth';
import { schema } from './schema';

export const load: PageServerLoad = async ({ cookies }) => {
	await requireUser(cookies);

	const form = await superValidate(zod(schema));

	return {
		form,
	};
};

export const actions: Actions = {
	default: async ({ request }) => {
		const form = await superValidate(request, zod(schema));
		if (!form.valid) {
			return fail(400, { form });
		}

		return message(form, { type: 'success', text: 'File uploaded' });
	},
};
