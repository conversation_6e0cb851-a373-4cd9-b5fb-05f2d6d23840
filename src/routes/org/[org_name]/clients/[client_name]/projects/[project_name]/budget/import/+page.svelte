<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import {
		displaySize,
		FileDropZone,
		MEGABYTE,
		type FileDropZoneProps,
	} from '$lib/components/ui/file-drop-zone';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { XIcon } from '@lucide/svelte';
	import { toast } from 'svelte-sonner';
	import { type SuperValidated, superForm, filesProxy } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import * as XLSX from 'xlsx';
	import { schema, type Schema } from './schema';

	const { data }: { data: PageData & { form: SuperValidated<Schema> } } = $props();
	const formHandler = superForm<Schema>(data.form, { validators: zodClient(schema) });
	const { enhance, message } = formHandler;

	interface ParsedRow {
		[key: string]: string | number | null;
	}

	let rows = $state<ParsedRow[]>([]);

	const files = filesProxy(formHand<PERSON>, 'attachments');

	const onUpload: FileDropZoneProps['onUpload'] = async (uploadedFiles) => {
		files.set([...Array.from($files), ...uploadedFiles]);

		if (uploadedFiles.length > 0) {
			await parseFile(uploadedFiles[0]);
		}
	};

	const onFileRejected: FileDropZoneProps['onFileRejected'] = async ({ reason, file }) => {
		toast.error(`${file.name} failed to upload!`, { description: reason });
	};

	async function parseFile(file: File) {
		const buffer = await file.arrayBuffer();
		const workbook = XLSX.read(buffer, { type: 'array' });
		const sheet = workbook.Sheets[workbook.SheetNames[0]];
		rows = XLSX.utils.sheet_to_json<ParsedRow>(sheet, { defval: null });
	}

	message.subscribe((msg) => {
		if (msg) {
			toast.success(msg.text, { description: 'Your file was uploaded.' });
		}
	});
</script>

<svelte:head>
	<title>Import Budget - {data.project.name} - {data.project.client.name}</title>
</svelte:head>

<div class="container mx-auto py-8">
	<h1 class="mb-4 text-3xl font-bold">Import Budget</h1>
	<p class="text-muted-foreground mb-6">
		Upload a CostX Excel export to create your budget.
		<!-- TODO: detailed instructions for exporting from CostX -->
	</p>

	<form method="POST" enctype="multipart/form-data" use:enhance class="flex w-full flex-col gap-2">
		<FileDropZone
			{onUpload}
			{onFileRejected}
			maxFileSize={10 * MEGABYTE}
			accept=".xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
			fileCount={$files.length}
		/>
		<input name="attachments" type="file" bind:files={$files} class="hidden" />
		<div class="flex flex-col gap-2">
			{#each Array.from($files) as file, i (file.name)}
				<div class="flex place-items-center justify-between gap-2">
					<div class="flex flex-col">
						<span>{file.name}</span>
						<span class="text-muted-foreground text-xs">{displaySize(file.size)}</span>
					</div>
					<Button
						variant="outline"
						size="icon"
						onclick={() => {
							files.set([...Array.from($files).slice(0, i), ...Array.from($files).slice(i + 1)]);
							rows = [];
						}}
					>
						<XIcon />
					</Button>
				</div>
			{/each}
		</div>
		<Button type="submit" class="w-fit">Upload</Button>
	</form>
</div>

{#if rows.length > 0}
	<div class="container mx-auto mt-8 overflow-x-auto py-8">
		<h2 class="mb-4 text-xl font-semibold">Verify Parsed Data</h2>
		<Table class="min-w-full text-sm">
			<TableHeader>
				<TableRow>
					{#each Object.keys(rows[0]) as key (key)}
						<TableHead>{key}</TableHead>
					{/each}
				</TableRow>
			</TableHeader>
			<TableBody>
				{#each rows as row (row)}
					<TableRow>
						{#each Object.keys(rows[0]) as key (key)}
							<TableCell>{row[key]}</TableCell>
						{/each}
					</TableRow>
				{/each}
			</TableBody>
		</Table>
		{#if rows.length > 20}
			<p class="text-muted-foreground mt-2 text-sm">Showing first 20 rows of {rows.length}.</p>
		{/if}
	</div>
{/if}
